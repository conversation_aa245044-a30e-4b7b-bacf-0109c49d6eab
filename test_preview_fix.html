<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Тест превью сообщений</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-info {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-info h2 {
            color: #333;
            margin-top: 0;
        }
        .test-steps {
            background: #e8f4fd;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #2196F3;
        }
        .test-steps ol {
            margin: 0;
            padding-left: 20px;
        }
        .test-steps li {
            margin-bottom: 8px;
        }
        .expected-result {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #4CAF50;
            margin-top: 15px;
        }
        .debug-info {
            background: #fff3cd;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #ffc107;
            margin-top: 15px;
        }
        .clear-storage {
            background: #f8d7da;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #dc3545;
            margin-top: 15px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .danger {
            background: #dc3545;
        }
        .danger:hover {
            background: #c82333;
        }
    </style>
</head>
<body>
    <div class="test-info">
        <h2>🎯 Тест превью сообщений при первом посещении</h2>
        
        <div class="test-steps">
            <h3>Шаги для тестирования:</h3>
            <ol>
                <li><strong>Очистите localStorage</strong> (кнопка ниже)</li>
                <li><strong>Перезагрузите страницу</strong> (F5 или Ctrl+R)</li>
                <li><strong>Дождитесь автооткрытия чата</strong> (должно произойти автоматически)</li>
                <li><strong>Проверьте консоль браузера</strong> (F12 → Console) для отладочной информации</li>
            </ol>
        </div>
        
        <div class="expected-result">
            <h3>✅ Ожидаемый результат:</h3>
            <p>После автооткрытия чата должны появиться <strong>2 превью сообщения</strong> с задержкой:</p>
            <ul>
                <li><strong>Первое сообщение</strong> (через ~3 сек): "Здравствуйте. Подскажите, кто виноват в заливе - соседи или УК?"</li>
                <li><strong>Второе сообщение</strong> (через ~6 сек): "Чтобы 100% получить компенсацию, важен корректный акт о заливе. Вы получили акт?"</li>
            </ul>
            <p>Сообщения должны иметь <strong>слегка прозрачный вид</strong> и <strong>тонкую цветную рамку</strong>.</p>
        </div>
        
        <div class="debug-info">
            <h3>🔍 Отладочная информация в консоли:</h3>
            <p>Следите за сообщениями в консоли браузера, которые начинаются с:</p>
            <ul>
                <li><code>[Main]</code> - инициализация модулей</li>
                <li><code>ChatHandler:</code> - загрузка данных чата</li>
                <li><code>UIChat:</code> - логика показа превью</li>
            </ul>
        </div>
        
        <div class="clear-storage">
            <h3>⚠️ Управление тестом:</h3>
            <button onclick="clearStorage()">🗑️ Очистить localStorage (имитация нового пользователя)</button>
            <button onclick="location.reload()" class="danger">🔄 Перезагрузить страницу</button>
            <button onclick="openConsole()">🔍 Открыть консоль браузера</button>
        </div>
    </div>

    <script>
        function clearStorage() {
            localStorage.clear();
            sessionStorage.clear();
            alert('✅ localStorage и sessionStorage очищены!\n\nТеперь перезагрузите страницу для тестирования.');
        }
        
        function openConsole() {
            alert('Нажмите F12 или Ctrl+Shift+I для открытия консоли браузера');
        }
        
        // Показываем текущее состояние localStorage
        window.addEventListener('load', function() {
            const hasVisited = localStorage.getItem('chatUserVisited');
            const chatState = localStorage.getItem('chatState');
            
            console.log('=== ТЕКУЩЕЕ СОСТОЯНИЕ LOCALSTORAGE ===');
            console.log('chatUserVisited:', hasVisited);
            console.log('chatState:', chatState);
            console.log('=====================================');
            
            if (hasVisited) {
                console.warn('⚠️ ВНИМАНИЕ: Пользователь уже посещал сайт. Для тестирования первого посещения очистите localStorage.');
            } else {
                console.log('✅ Пользователь новый - автооткрытие должно сработать');
            }
        });
    </script>

    <!-- Подключаем чат -->
    <script type="module" src="chat-admin/chat-js/chat-loader.js"></script>
</body>
</html>
