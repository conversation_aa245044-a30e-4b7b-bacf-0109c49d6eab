<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Финальный тест превью сообщений</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f0f0f0;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .status.success { background: #d4edda; border-left: 4px solid #28a745; }
        .status.error { background: #f8d7da; border-left: 4px solid #dc3545; }
        .status.warning { background: #fff3cd; border-left: 4px solid #ffc107; }
        .status.info { background: #d1ecf1; border-left: 4px solid #17a2b8; }
        
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .danger { background: #dc3545; }
        .danger:hover { background: #c82333; }
        .success { background: #28a745; }
        .success:hover { background: #218838; }
        
        pre {
            background: #f1f1f1;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
            font-size: 12px;
        }
        
        .step {
            margin: 15px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .step h3 {
            margin-top: 0;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 Финальный тест превью сообщений</h1>
        <p>Этот тест проверяет весь процесс работы превью сообщений при первом посещении.</p>
        
        <div class="step">
            <h3>Шаг 1: Подготовка</h3>
            <button onclick="clearStorageAndPrepare()" class="danger">🗑️ Очистить localStorage (имитация нового пользователя)</button>
            <div id="step1-status"></div>
        </div>
        
        <div class="step">
            <h3>Шаг 2: Проверка API</h3>
            <button onclick="testApiSettings()">🔍 Проверить API настройки</button>
            <div id="step2-status"></div>
        </div>
        
        <div class="step">
            <h3>Шаг 3: Тест автооткрытия</h3>
            <button onclick="reloadAndTest()" class="success">🚀 Перезагрузить и тестировать автооткрытие</button>
            <div id="step3-status"></div>
        </div>
        
        <div class="step">
            <h3>Шаг 4: Ручной тест</h3>
            <button onclick="manualPreviewTest()">🎯 Ручной тест превью</button>
            <button onclick="manualChatOpen()">💬 Ручное открытие чата</button>
            <div id="step4-status"></div>
        </div>
        
        <div class="container">
            <h3>📊 Лог событий</h3>
            <button onclick="clearLog()">🗑️ Очистить лог</button>
            <div id="event-log"></div>
        </div>
    </div>

    <script>
        let eventLog = [];
        
        function logEvent(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            eventLog.push({ timestamp, message, type });
            updateEventLog();
            console.log(`[${timestamp}] ${message}`);
        }
        
        function updateEventLog() {
            const logDiv = document.getElementById('event-log');
            logDiv.innerHTML = eventLog.map(event => 
                `<div class="status ${event.type}">
                    <strong>${event.timestamp}:</strong> ${event.message}
                </div>`
            ).join('');
        }
        
        function clearLog() {
            eventLog = [];
            updateEventLog();
        }
        
        function updateStepStatus(stepId, message, type = 'info') {
            const statusDiv = document.getElementById(`${stepId}-status`);
            statusDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
        }
        
        function clearStorageAndPrepare() {
            localStorage.clear();
            sessionStorage.clear();
            logEvent('localStorage и sessionStorage очищены', 'success');
            updateStepStatus('step1', '✅ Хранилище очищено. Теперь вы новый пользователь.', 'success');
        }
        
        async function testApiSettings() {
            try {
                logEvent('Тестируем API настройки...', 'info');
                
                const response = await fetch('chat-admin/index.php?page=api&action=getSettings');
                const data = await response.json();
                
                if (data.status === 'success' && data.settings) {
                    const settings = data.settings;
                    const hasPreview1 = settings.preview_message_1 && settings.preview_message_1.trim();
                    const hasPreview2 = settings.preview_message_2 && settings.preview_message_2.trim();
                    const isEnabled = settings.preview_messages_enabled == 1;
                    
                    if (hasPreview1 && hasPreview2 && isEnabled) {
                        logEvent('✅ API настройки корректны', 'success');
                        updateStepStatus('step2', 
                            `✅ API работает корректно:<br>
                            • preview_messages_enabled: ${isEnabled}<br>
                            • preview_message_delay: ${settings.preview_message_delay}ms<br>
                            • preview_message_1: "${settings.preview_message_1}"<br>
                            • preview_message_2: "${settings.preview_message_2}"`, 'success');
                    } else {
                        logEvent('❌ Проблемы с настройками превью', 'error');
                        updateStepStatus('step2', 
                            `❌ Проблемы с настройками:<br>
                            • Включены: ${isEnabled}<br>
                            • Сообщение 1: ${hasPreview1 ? 'есть' : 'отсутствует'}<br>
                            • Сообщение 2: ${hasPreview2 ? 'есть' : 'отсутствует'}`, 'error');
                    }
                } else {
                    logEvent('❌ API вернул ошибку', 'error');
                    updateStepStatus('step2', '❌ API вернул ошибку: ' + (data.message || 'неизвестная ошибка'), 'error');
                }
            } catch (error) {
                logEvent('❌ Ошибка запроса к API: ' + error.message, 'error');
                updateStepStatus('step2', '❌ Ошибка запроса к API: ' + error.message, 'error');
            }
        }
        
        function reloadAndTest() {
            logEvent('Перезагружаем страницу для теста автооткрытия...', 'warning');
            updateStepStatus('step3', '🔄 Перезагружаем страницу...', 'warning');
            setTimeout(() => {
                location.reload();
            }, 1000);
        }
        
        async function manualPreviewTest() {
            try {
                logEvent('Запускаем ручной тест превью...', 'info');
                
                const { checkAndShowPreviewForEmptyChat } = await import('./chat-admin/chat-js/uiChat.js');
                await checkAndShowPreviewForEmptyChat();
                
                logEvent('✅ Ручной тест превью завершен', 'success');
                updateStepStatus('step4', '✅ Ручной тест превью выполнен. Проверьте консоль браузера.', 'success');
            } catch (error) {
                logEvent('❌ Ошибка ручного теста: ' + error.message, 'error');
                updateStepStatus('step4', '❌ Ошибка ручного теста: ' + error.message, 'error');
            }
        }
        
        async function manualChatOpen() {
            try {
                logEvent('Открываем чат вручную...', 'info');
                
                const { openChatWindow } = await import('./chat-admin/chat-js/uiChat.js');
                openChatWindow();
                
                logEvent('✅ Чат открыт вручную', 'success');
                updateStepStatus('step4', '✅ Чат открыт вручную. Превью должны появиться через 100мс + задержка.', 'success');
            } catch (error) {
                logEvent('❌ Ошибка открытия чата: ' + error.message, 'error');
                updateStepStatus('step4', '❌ Ошибка открытия чата: ' + error.message, 'error');
            }
        }
        
        // Проверяем состояние при загрузке
        window.addEventListener('load', function() {
            const hasVisited = localStorage.getItem('chatUserVisited');
            
            if (hasVisited) {
                logEvent('⚠️ Пользователь уже посещал сайт', 'warning');
                updateStepStatus('step1', '⚠️ Пользователь уже посещал сайт. Очистите localStorage для теста.', 'warning');
            } else {
                logEvent('✅ Новый пользователь - автооткрытие должно сработать', 'success');
                updateStepStatus('step1', '✅ Новый пользователь - автооткрытие должно сработать', 'success');
                updateStepStatus('step3', '⏳ Ожидаем автооткрытие чата...', 'info');
            }
            
            // Отслеживаем изменения состояния чата
            let checkCount = 0;
            const checkInterval = setInterval(() => {
                checkCount++;
                const chatState = localStorage.getItem('chatState');
                
                if (chatState) {
                    try {
                        const state = JSON.parse(chatState);
                        if (state.isChatOpen) {
                            logEvent('✅ Чат открылся автоматически!', 'success');
                            updateStepStatus('step3', '✅ Чат открылся автоматически! Ожидаем превью сообщения...', 'success');
                            clearInterval(checkInterval);
                        }
                    } catch (e) {
                        // Игнорируем ошибки парсинга
                    }
                }
                
                // Останавливаем проверку через 30 секунд
                if (checkCount > 60) {
                    logEvent('⏰ Таймаут ожидания автооткрытия', 'warning');
                    updateStepStatus('step3', '⏰ Автооткрытие не произошло за 30 секунд', 'warning');
                    clearInterval(checkInterval);
                }
            }, 500);
        });
    </script>

    <!-- Подключаем чат -->
    <script type="module" src="chat-admin/chat-js/chat-loader.js"></script>
</body>
</html>
