<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Простой тест превью</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f0f0f0;
        }
        .info {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .danger {
            background: #dc3545;
        }
        .danger:hover {
            background: #c82333;
        }
    </style>
</head>
<body>
    <div class="info">
        <h2>Простой тест превью сообщений</h2>
        <p>Эта страница предназначена для тестирования превью сообщений при первом посещении.</p>
        
        <button onclick="clearAndReload()">🗑️ Очистить localStorage и перезагрузить</button>
        <button onclick="showConsole()">🔍 Показать инструкции для консоли</button>
        <button onclick="testPreviewManually()">🎯 Тест превью вручную</button>
        <button onclick="openChatManually()">🚀 Открыть чат вручную</button>
        
        <div id="status" style="margin-top: 15px; padding: 10px; background: #e9ecef; border-radius: 5px;">
            <strong>Статус:</strong> <span id="statusText">Загрузка...</span>
        </div>
    </div>

    <script>
        function clearAndReload() {
            localStorage.clear();
            sessionStorage.clear();
            location.reload();
        }
        
        function showConsole() {
            alert('Откройте консоль браузера (F12) и следите за сообщениями:\n\n' +
                  '🎯 [Main] - инициализация\n' +
                  '🚀 UIChat: - открытие чата\n' +
                  '🎯 UIChat: - проверка превью\n\n' +
                  'Превью должны появиться через 3-6 секунд после открытия чата.');
        }

        async function testPreviewManually() {
            try {
                console.log('🎯 Ручной тест превью...');
                // Импортируем функцию из модуля
                const { checkAndShowPreviewForEmptyChat } = await import('./chat-admin/chat-js/uiChat.js');
                await checkAndShowPreviewForEmptyChat();
                console.log('✅ Ручной тест превью завершен');
            } catch (error) {
                console.error('❌ Ошибка ручного теста превью:', error);
                alert('Ошибка: ' + error.message);
            }
        }

        async function openChatManually() {
            try {
                console.log('🚀 Ручное открытие чата...');
                const { openChatWindow } = await import('./chat-admin/chat-js/uiChat.js');
                openChatWindow();
                console.log('✅ Чат открыт вручную');
            } catch (error) {
                console.error('❌ Ошибка открытия чата:', error);
                alert('Ошибка: ' + error.message);
            }
        }
        
        function updateStatus(text) {
            document.getElementById('statusText').textContent = text;
        }
        
        // Проверяем состояние при загрузке
        window.addEventListener('load', function() {
            const hasVisited = localStorage.getItem('chatUserVisited');
            
            if (hasVisited) {
                updateStatus('Пользователь уже посещал сайт. Нажмите кнопку очистки для тестирования.');
            } else {
                updateStatus('Новый пользователь - чат должен открыться автоматически с превью.');
            }
            
            console.log('=== ТЕСТ ПРЕВЬЮ СООБЩЕНИЙ ===');
            console.log('chatUserVisited:', hasVisited);
            console.log('Ожидаем автооткрытие чата...');
        });
        
        // Отслеживаем изменения в localStorage
        let checkInterval = setInterval(() => {
            const isChatOpen = localStorage.getItem('chatState');
            if (isChatOpen && JSON.parse(isChatOpen).isChatOpen) {
                updateStatus('Чат открыт! Ожидаем превью сообщения...');
                clearInterval(checkInterval);
            }
        }, 500);
    </script>

    <!-- Подключаем чат -->
    <script type="module" src="chat-admin/chat-js/chat-loader.js"></script>
</body>
</html>
