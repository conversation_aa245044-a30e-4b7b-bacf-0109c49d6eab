<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Тест API напрямую</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f0f0f0;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-top: 15px;
            border-left: 4px solid #007bff;
        }
        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        .success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        pre {
            background: #f1f1f1;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>Тест API напрямую</h2>
        <p>Этот тест проверяет, что API возвращает правильные настройки превью.</p>
        
        <button onclick="testApiSettings()">🔍 Тест API getSettings</button>
        <button onclick="testApiTexts()">📝 Тест получения текстов</button>
        <button onclick="clearResults()">🗑️ Очистить результаты</button>
        
        <div id="results"></div>
    </div>

    <script>
        function addResult(title, content, type = 'result') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.innerHTML = `
                <h3>${title}</h3>
                <pre>${content}</pre>
            `;
            resultsDiv.appendChild(resultDiv);
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        async function testApiSettings() {
            try {
                console.log('🔍 Тестируем API getSettings...');
                
                const response = await fetch('chat-admin/index.php?page=api&action=getSettings');
                const data = await response.json();
                
                console.log('API Response:', data);
                
                if (data.status === 'success') {
                    addResult('✅ API getSettings - Успех', JSON.stringify(data, null, 2), 'success');
                    
                    // Проверяем наличие превью сообщений
                    const settings = data.settings;
                    if (settings.preview_message_1 && settings.preview_message_2) {
                        addResult('✅ Превью сообщения найдены', 
                            `preview_message_1: "${settings.preview_message_1}"\n` +
                            `preview_message_2: "${settings.preview_message_2}"\n` +
                            `preview_messages_enabled: ${settings.preview_messages_enabled}\n` +
                            `preview_message_delay: ${settings.preview_message_delay}`, 'success');
                    } else {
                        addResult('❌ Превью сообщения НЕ найдены', 
                            `preview_message_1: "${settings.preview_message_1 || 'ПУСТО'}"\n` +
                            `preview_message_2: "${settings.preview_message_2 || 'ПУСТО'}"`, 'error');
                    }
                } else {
                    addResult('❌ API getSettings - Ошибка', JSON.stringify(data, null, 2), 'error');
                }
                
            } catch (error) {
                console.error('Ошибка API:', error);
                addResult('❌ Ошибка запроса API', error.message, 'error');
            }
        }
        
        async function testApiTexts() {
            try {
                console.log('📝 Тестируем получение текстов...');
                
                // Тестируем импорт модуля API
                const apiModule = await import('./chat-admin/chat-js/api.js');
                console.log('API модуль импортирован:', apiModule);
                
                const settingsResponse = await apiModule.getSettings();
                console.log('Settings response:', settingsResponse);
                
                if (settingsResponse && settingsResponse.settings) {
                    addResult('✅ API модуль работает', JSON.stringify(settingsResponse, null, 2), 'success');
                } else {
                    addResult('❌ API модуль не работает', 'Нет данных в ответе', 'error');
                }
                
            } catch (error) {
                console.error('Ошибка модуля API:', error);
                addResult('❌ Ошибка модуля API', error.message, 'error');
            }
        }
    </script>
</body>
</html>
