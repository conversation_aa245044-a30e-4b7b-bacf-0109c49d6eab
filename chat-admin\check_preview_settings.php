<?php
declare(strict_types=1);

require_once 'app/Models/Database.php';

use App\Models\Database;

try {
    $db = Database::getInstance();
    
    echo "Проверка настроек превью в базе данных:\n";
    echo "======================================\n";
    
    $result = $db->query('SELECT preview_messages_enabled, preview_message_delay, preview_hide_greeting FROM api_settings WHERE id = 1');
    $settings = $result->fetchArray(SQLITE3_ASSOC);
    
    if ($settings) {
        echo "preview_messages_enabled: " . ($settings['preview_messages_enabled'] ?? 'NULL') . "\n";
        echo "preview_message_delay: " . ($settings['preview_message_delay'] ?? 'NULL') . "\n";
        echo "preview_hide_greeting: " . ($settings['preview_hide_greeting'] ?? 'NULL') . "\n";
        
        // Если превью отключены - включаем
        if ($settings['preview_messages_enabled'] != 1) {
            echo "\nВКЛЮЧАЕМ превью сообщения...\n";
            $db->exec("UPDATE api_settings SET preview_messages_enabled = 1 WHERE id = 1");
            echo "✅ Превью сообщения ВКЛЮЧЕНЫ!\n";
        } else {
            echo "\n✅ Превью сообщения уже ВКЛЮЧЕНЫ!\n";
        }
        
        // Проверяем задержку
        if (empty($settings['preview_message_delay']) || $settings['preview_message_delay'] < 1000) {
            echo "\nУстанавливаем задержку превью в 3000мс...\n";
            $db->exec("UPDATE api_settings SET preview_message_delay = 3000 WHERE id = 1");
            echo "✅ Задержка превью установлена!\n";
        }
        
    } else {
        echo "❌ Настройки не найдены!\n";
    }
    
} catch (Exception $e) {
    echo "ОШИБКА: " . $e->getMessage() . "\n";
}
?>
