// chat-js/chatHandler.js
import * as api from "./api.js";
import * as ui from "./uiChat.js";
import * as state from "./state.js";
import { showNotificationAbove, findOrFail } from "./domUtils.js";
import textManager from "./textManager.js"; // Импортируем менеджер текстов
import analyticsCollector from "./analyticsCollector.js"; // Импортируем аналитический модуль
// Импортируем новую функцию для добавления контента
const { addChatMessageElement, appendChatMessageContent, updateChatMessageElementId, removeMessagesAfterElement, restoreOriginalMessageView } = ui;

export async function initChat() {
  console.log("ChatHandler: Initializing...");
  ui.initChatUI(
      handleSendMessage, // onSendMessage
      handleOpenSession, // onOpenSession
      handleCreateSession, // onCreateSession
      handleDeleteSession, // onDeleteSession
      handleUpdateSessionTitle, // onUpdateSessionTitle
      handleEditMessage, // onEditMessage (теперь принимает event)
      handleCopyMessage // onCopyMessage (теперь принимает event)
  );

  // Инициализируем флаг первого входа
  state.initFirstTimeUser();

  // Загружаем начальное состояние
  const savedState = localStorage.getItem("chatState");
  if (savedState) {
    const parsedState = JSON.parse(savedState);
    if (parsedState.isChatOpen) {
      ui.openChatWindow(); // Открываем окно, если было открыто
    }
  }

  await loadInitialData();
  console.log("ChatHandler: Initialization complete.");
}

async function loadInitialData() {
  console.log("ChatHandler: Starting loadInitialData..."); // LOGGING
  try {
      // Reinitialize text manager to ensure new texts are loaded
      await textManager.initialize();
      console.log("[ChatHandler] Text manager reinitialized for new texts");
      
      console.log("ChatHandler: Fetching sessions and active session..."); // LOGGING
      // Загружаем сессии и активную сессию параллельно
      const [sessionsData, activeSessionData] = await Promise.all([
          api.getUserSessions(),
          api.getActiveSession(),
      ]);
      console.log("ChatHandler: Sessions data received:", sessionsData); // LOGGING
      console.log("ChatHandler: Active session data received:", activeSessionData); // LOGGING

    // Обновляем список сессий в состоянии и UI
    const sessions = sessionsData.sessions || [];
    state.setState({ chatSessions: sessions });
    console.log(`ChatHandler: Set ${sessions.length} sessions in state.`); // LOGGING
    ui.renderSessionList(
        sessions, // Передаем полученные сессии
        state.getState().currentSessionId // Текущий ID еще не установлен, будет null или старый
    );
    console.log("ChatHandler: Session list rendered."); // LOGGING

    // Если у пользователя нет сессий вообще и чат открыт - показываем превью
    // НО только если нет истории сообщений
    if (sessions.length === 0 && state.getState().isChatOpen && state.getState().messageHistory.length === 0) {
        console.log("ChatHandler: No sessions found, chat is open, and no message history - showing preview messages");
        try {
            const { checkAndShowPreviewForEmptyChat } = await import('./uiChat.js');
            await checkAndShowPreviewForEmptyChat();
        } catch (error) {
            console.error("ChatHandler: Error showing preview for no sessions:", error);
        }
    }

    // Устанавливаем активную сессию
    const activeSessionId = activeSessionData?.session_id; // Используем optional chaining
    console.log(`ChatHandler: Active session ID from API: ${activeSessionId}`); // LOGGING
    console.log(`ChatHandler: Current state - messageHistory.length: ${state.getState().messageHistory.length}, isFirstTimeUser: ${state.getState().isFirstTimeUser}`); // LOGGING
    if (activeSessionId) {
        state.setState({ currentSessionId: activeSessionId });
        console.log(`ChatHandler: Set currentSessionId in state: ${activeSessionId}`); // LOGGING
        ui.highlightActiveSession(activeSessionId); // Подсвечиваем активную
        console.log(`ChatHandler: Loading history for ${activeSessionId} (regardless of chat open state)...`); // LOGGING
        await loadChatHistory(activeSessionId);
    } else {
        console.log("ChatHandler: No active session ID found from API."); // LOGGING
        state.setState({ currentSessionId: null });
        if (
            state.getState().messageHistory.length === 0 &&
            state.getState().isFirstTimeUser === true
        ) {
            console.log("ChatHandler: No active session, empty history, and first time user - creating initial session."); // LOGGING

            // Создаем новую сессию для начальных сообщений
            try {
                const createData = await api.createSession("Начальный чат");
                const newSessionId = createData.session_id;
                state.setState({ currentSessionId: newSessionId });

                // Превью будет показано при открытии чата через toggleChatWindow()
                console.log("ChatHandler: Session created, preview will be shown on chat open");

                // Обновляем список сессий
                const sessionsData = await api.getUserSessions();
                state.setState({ chatSessions: sessionsData.sessions || [] });
                ui.renderSessionList(state.getState().chatSessions, newSessionId);
                ui.highlightActiveSession(newSessionId);

                // Отмечаем, что пользователь больше не новичок
                state.markUserAsReturning();

            } catch (error) {
                console.error("ChatHandler: Error creating initial session:", error);
                // Fallback - показываем стандартное приветствие
                ui.addChatMessageElement(
                    "assistant",
                    textManager.getText('initial_greeting'),
                    "initial-greeting"
                );
                state.markUserAsReturning();
            }
        } else {
            console.log("ChatHandler: Initial messages skipped (history not empty or not first time user)."); // LOGGING

            // УБИРАЕМ ЭТОТ ВЫЗОВ - превью будет показано через openChatWindow()
            // Проверяем, если чат открыт и полностью пустой - показываем превью
            // if (state.getState().isChatOpen && state.getState().messageHistory.length === 0) {
            //     console.log("ChatHandler: Chat is open and empty, showing preview messages");
            //     try {
            //         const { checkAndShowPreviewForEmptyChat } = await import('./uiChat.js');
            //         await checkAndShowPreviewForEmptyChat();
            //     } catch (error) {
            //         console.error("ChatHandler: Error showing preview for empty chat:", error);
            //     }
            // }
        }
    }
  } catch (error) {
    console.error("ChatHandler: Error loading initial data:", error);
    if (state.getState().isChatOpen) {
      ui.addChatMessageElement(
        "assistant",
        `Ошибка загрузки данных: ${error.message}`,
        "error-load"
      );
    }
  }
}

async function loadChatHistory(sessionId) {
  if (!sessionId) return;
  console.log(`ChatHandler: Loading history for session ${sessionId}`);
  ui.clearMessages();
  state.clearMessageHistory();
  // Можно показать индикатор загрузки истории
  try {
    const data = await api.getMessages(sessionId);
    if (data.messages && data.messages.length > 0) {
      // Сортируем по id (по возрастанию)
      data.messages.sort((a, b) => a.id - b.id);
      data.messages.forEach((msg) => {
        // Добавляем в UI и в историю состояния
        ui.addChatMessageElement(msg.role, msg.content, msg.id); // Используем role и content
        state.addMessageToHistory({
          sender: msg.role,
          text: msg.content,
          id: msg.id,
        });
      });
    } else {
      // История пуста - проверяем, нужно ли показать превью
      console.log("ChatHandler: History is empty, checking if preview should be shown");

      // УБИРАЕМ ЭТОТ ВЫЗОВ - превью будет показано через openChatWindow()
      // if (state.getState().isChatOpen) {
      //   // Если чат открыт и история пуста, пытаемся показать превью
      //   console.log("ChatHandler: Chat is open and history empty, attempting to show preview");
      //
      //   // Импортируем функцию показа превью
      //   const { checkAndShowPreviewForEmptyChat } = await import('./uiChat.js');
      //
      //   // Пытаемся показать превью
      //   try {
      //     await checkAndShowPreviewForEmptyChat();
      //     console.log("ChatHandler: Preview attempt completed");
      //   } catch (previewError) {
      //     console.error("ChatHandler: Error showing preview:", previewError);
      //     // Fallback - показываем стандартное сообщение о пустом чате
      //     ui.addChatMessageElement(
      //       "assistant",
      //       textManager.getText('empty_chat_message'),
      //       "empty-chat"
      //     );
      //   }
      // }
    }
  } catch (error) {
    console.error("ChatHandler: Error loading history:", error);
    if (state.getState().isChatOpen) {
      ui.addChatMessageElement(
        "assistant",
        `Не удалось загрузить историю: ${error.message}`,
        "error-history"
      );
    }
  } finally {
    // Скрыть индикатор загрузки истории
    ui.scrollToBottom();
  }
}

// --- Обработчики, вызываемые из UI ---

async function handleSendMessage(messageText) {
    ui.disableSendButton(true);
    ui.showTypingIndicator(true); // Показываем индикатор сразу
    ui.clearChatInput();

    const userTempId = `temp_user_${Date.now()}`;
    addChatMessageElement("user", messageText, userTempId);
    state.addMessageToHistory({ sender: "user", text: messageText, id: userTempId });

    // Проверяем, содержит ли сообщение контактные данные
    const hasContacts = analyticsCollector.hasContactData(messageText);
    if (hasContacts) {
        console.log("ChatHandler: Contact data detected, will send analytics data");
        // Обрабатываем отправку контактных данных (отправляем цель в Яндекс.Метрику)
        await analyticsCollector.handleContactSubmission(messageText);
    }

    let currentSessionId = state.getState().currentSessionId;

    try {
        // Получаем активную сессию с бэкенда для гарантии
        const activeSessionData = await api.getActiveSession();
        if (activeSessionData?.session_id) {
            currentSessionId = activeSessionData.session_id;
            state.setState({ currentSessionId: currentSessionId });
        }

        // 1. Создать сессию, если необходимо
        if (!currentSessionId) {
            console.log("ChatHandler: No active session, creating new one...");
            const createData = await api.createSession(
                messageText.substring(0, 30) + (messageText.length > 30 ? "..." : "")
            );
            currentSessionId = createData.session_id;
            state.setState({ currentSessionId });
            // Обновляем список сессий в UI
            const sessionsData = await api.getUserSessions();
            state.setState({ chatSessions: sessionsData.sessions || [] });
            ui.renderSessionList(state.getState().chatSessions, currentSessionId);
            ui.highlightActiveSession(currentSessionId);
            console.log(`ChatHandler: New session created: ${currentSessionId}`);
        }

        // 2. Сохраняем сообщение пользователя на сервере
        let userMessageId = null;
        try {
            // Собираем аналитические данные для отправки
            // ВАЖНО: Передаем аналитические данные только если есть контактные данные
            const analyticsData = hasContacts ? analyticsCollector.getAnalyticsData() : null;

            console.log("ChatHandler: hasContacts =", hasContacts);
            console.log("ChatHandler: analyticsData =", analyticsData);

            const saveUserResp = await api.saveUserMessage(currentSessionId, messageText, analyticsData);
            userMessageId = saveUserResp?.user_message_id;
            if (userMessageId) {
                updateChatMessageElementId(userTempId, userMessageId);
                state.updateMessageHistoryId(userTempId, userMessageId);
            }
        } catch (e) {
            console.error("ChatHandler: Failed to save user message before stream:", e);
            // Можно пометить сообщение как ошибочное
        }

        // 3. Создаем пустой элемент для ответа ассистента СРАЗУ
        const assistantTempId = `temp_assistant_${Date.now()}`;
        let assistantMessageElement = addChatMessageElement('assistant', '', assistantTempId);

        // 4. Подготовить историю сообщений для API
        const messagesForApi = []; // Историю больше не передаем
        console.log("ChatHandler: Sending message history to stream API:", messagesForApi);

        // 5. Инициировать потоковый запрос
        let fullAssistantReply = '';
        let streamErrorOccurred = false;

        api.streamMessage(currentSessionId, messagesForApi, {
            onTextChunk: (chunk) => {
                if (streamErrorOccurred) return;
                fullAssistantReply += chunk;
                // Отображаем сырой текст во время стриминга
                appendChatMessageContent(assistantMessageElement, chunk, false); // Передаем chunk и isFinalChunk=false
                ui.scrollToBottom();
            },
            onAudioChunk: (audioData) => {
                console.log("Received audio chunk (handler placeholder)");
            },
            onEnd: () => {
                if (streamErrorOccurred) return;
                console.log("ChatHandler: Stream ended.");
                if (assistantMessageElement && fullAssistantReply) {
                    // Финальный вызов для форматирования всего текста
                    console.log("ChatHandler: Calling final format in onEnd"); // Логгирование
                    appendChatMessageContent(assistantMessageElement, '', true); // Пустой chunk, isFinalChunk=true

                    // Сохраняем сообщение ассистента
                    api.saveAssistantMessage(currentSessionId, fullAssistantReply, assistantTempId)
                        .then(saveResponse => {
                            if (saveResponse && saveResponse.assistant_message_id) {
                                const finalAssistantId = saveResponse.assistant_message_id;
                                console.log(`ChatHandler: Assistant message saved with ID: ${finalAssistantId}`);
                                updateChatMessageElementId(assistantTempId, finalAssistantId);
                                state.updateMessageHistoryId(assistantTempId, finalAssistantId);
                            } else {
                                console.warn("ChatHandler: Save response did not contain assistant_message_id.", saveResponse);
                            }
                        })
                        .catch(saveError => {
                            console.error("ChatHandler: Error saving assistant message:", saveError);
                            if (assistantMessageElement) {
                                assistantMessageElement.classList.add('message-error-save');
                                assistantMessageElement.title = `Ошибка сохранения: ${saveError.message}`;
                            }
                        });

                    state.addMessageToHistory({
                        sender: 'assistant',
                        text: fullAssistantReply,
                        id: assistantTempId
                    });
                    console.log("ChatHandler: Assistant message added to state history with temp ID:", assistantTempId);

                } else if (!fullAssistantReply) {
                    console.warn("ChatHandler: Stream ended but no assistant message was generated.");
                    if (assistantMessageElement) {
                        assistantMessageElement.remove();
                    }
                    addChatMessageElement('assistant', textManager.getText('no_assistant_response'), `no_reply_${Date.now()}`);
                }
                ui.showTypingIndicator(false);
                ui.disableSendButton(false);
                ui.focusChatInput();
            },
            onError: (error) => {
                if (streamErrorOccurred) return;
                streamErrorOccurred = true;
                console.error("ChatHandler: Stream error:", error);
                if (assistantMessageElement) {
                    assistantMessageElement.remove();
                }
                let errorMessage = 'Ошибка получения ответа';
                if (error.message.includes('empty response')) {
                    errorMessage = 'Ассистент не смог сформировать ответ. Попробуйте переформулировать вопрос.';
                } else {
                    errorMessage = `Ошибка: ${error.message}`;
                }
                addChatMessageElement('assistant', errorMessage, `error_${Date.now()}`);
                ui.showTypingIndicator(false);
                ui.disableSendButton(false);
                ui.focusChatInput();
            }
        });

    } catch (error) {
        // Ошибки на этапе создания сессии или подготовки запроса
        console.error("ChatHandler: Error before starting stream:", error);
        addChatMessageElement('assistant', `Ошибка отправки: ${error.message}`, `error_${Date.now()}`);
        // Убираем индикатор и разблокируем кнопку
        ui.showTypingIndicator(false);
        ui.disableSendButton(false);
        // // Удаляем временное сообщение пользователя из UI и состояния - ЗАКОММЕНТИРОВАНО
        // const tempElement = document.querySelector(`[data-id="${userTempId}"]`);
        // if (tempElement) tempElement.remove();
        // const tempIndex = state.getState().messageHistory.findIndex((m) => m.id === userTempId);
        // if (tempIndex !== -1) {
        //      const currentHistory = state.getState().messageHistory;
        //      currentHistory.splice(tempIndex, 1);
        //      state.setState({ messageHistory: [...currentHistory] });
        // }
    }
    // Не ставим finally здесь, так как разблокировка кнопки происходит в onEnd/onError
}


async function handleOpenSession(sessionId) {
  console.log(`ChatHandler: Opening session ${sessionId}`);
  state.setState({ currentSessionId: sessionId });
  // ui.highlightActiveSession(sessionId); // Уже делается в uiChat при клике
  await loadChatHistory(sessionId);
}

async function handleCreateSession() {
  console.log("ChatHandler: Creating new session via button...");
  ui.disableSendButton(true); // Блокируем отправку на всякий случай
  ui.showTypingIndicator(true);
  try {
    const createData = await api.createSession("Новый чат"); // Создаем пустой чат
    state.setState({ currentSessionId: createData.session_id });
    state.clearMessageHistory();
    ui.clearMessages();

    // Обновляем список сессий
    const sessionsData = await api.getUserSessions();
    state.setState({ chatSessions: sessionsData.sessions || [] });
    ui.renderSessionList(state.getState().chatSessions, createData.session_id);
    ui.highlightActiveSession(createData.session_id);

    // Показываем уведомление над полем ввода
    try {
        const chatInputElement = findOrFail('#chatInput'); // Находим поле ввода
        showNotificationAbove(textManager.getText('notification_new_chat_created'), chatInputElement);
    } catch (e) {
        console.warn("ChatHandler: Could not find #chatInput to show notification.", e);
    }

    // УБИРАЕМ ПРЕВЬЮ ДЛЯ НОВОЙ СЕССИИ - превью только для пустых чатов
    // Пытаемся показать превью для нового пустого чата
    console.log("ChatHandler: New session created, showing notification (no preview for new sessions)");

    // Показываем стандартное уведомление о создании нового чата
    ui.addChatMessageElement(
      "assistant",
      textManager.getText('notification_new_chat_created'),
      "new-chat-created"
    );

  } catch (error) {
    console.error("ChatHandler: Error creating session:", error);
    ui.addChatMessageElement(
      "assistant",
      `Не удалось создать чат: ${error.message}`,
      `error_${Date.now()}`
    );
  } finally {
    ui.disableSendButton(false);
    ui.showTypingIndicator(false);
    ui.focusChatInput();
  }
}

async function handleDeleteSession(sessionId, listItemElement) {
  console.log(`ChatHandler: Deleting session ${sessionId}`);
  // Можно добавить индикатор удаления на listItemElement
  listItemElement.style.opacity = "0.5";
  try {
    await api.deleteSession(sessionId);
    // listItemElement.remove(); // Убираем ручное удаление, т.к. renderSessionList перерисует список
    // Обновляем состояние сессий
    const initialSessions = state.getState().chatSessions;
    console.log(`ChatHandler: Sessions before delete ${sessionId}:`, initialSessions.map(s => s.id)); // LOGGING
    const currentSessions = initialSessions.filter((s) => String(s.id) !== String(sessionId)); // Сравнение строк на всякий случай
    state.setState({ chatSessions: currentSessions });
    console.log(`ChatHandler: Sessions after delete ${sessionId}:`, state.getState().chatSessions.map(s => s.id)); // LOGGING
    // Если удалили текущую сессию
    if (state.getState().currentSessionId === sessionId) {
      state.setState({ currentSessionId: null });
      state.clearMessageHistory();
      ui.clearMessages();
      // Исправляем аргументы: sender, text, id
      ui.addChatMessageElement(
        "assistant",
        textManager.getText('chat_deleted_message'),
        "chat-deleted"
      );
    }

    // Если после удаления список чатов пуст — показываем превью сообщения
    if (state.getState().chatSessions.length === 0) {
      state.setState({ currentSessionId: null });
      state.clearMessageHistory();
      ui.clearMessages();

      // Показываем превью сообщения вместо стандартного уведомления
      // НО только если нет истории сообщений
      if (state.getState().isChatOpen && state.getState().messageHistory.length === 0) {
        console.log("ChatHandler: No sessions left and no message history, showing preview messages");
        try {
          const { checkAndShowPreviewForEmptyChat } = await import('./uiChat.js');
          await checkAndShowPreviewForEmptyChat();
        } catch (error) {
          console.error("ChatHandler: Error showing preview for no sessions:", error);
          // Fallback - показываем стандартное сообщение
          ui.addChatMessageElement(
            "assistant",
            textManager.getText('no_chats_message'),
            "no-chat"
          );
        }
      } else {
        // Если чат не открыт или есть история, показываем стандартное сообщение
        ui.addChatMessageElement(
          "assistant",
          textManager.getText('no_chats_message'),
          "no-chat"
        );
      }
    }

    // Всегда обновляем список сессий в UI после удаления,
    // renderSessionList сама решит, скрыть его или показать.
    // Передаем актуальные данные из состояния ПОСЛЕ обновления
    ui.renderSessionList(state.getState().chatSessions, state.getState().currentSessionId);
  } catch (error) {
    console.error(`ChatHandler: Error deleting session ${sessionId}:`, error);
    alert(textManager.getText('error_delete_chat') + error.message);
    listItemElement.style.opacity = "1"; // Возвращаем видимость при ошибке
  }
}

async function handleUpdateSessionTitle(sessionId, newTitle) {
  console.log(
    `ChatHandler: Updating session ${sessionId} title to "${newTitle}"`
  );
  try {
    await api.updateSessionTitle(sessionId, newTitle);
    // Обновляем название в состоянии
    const sessions = state.getState().chatSessions;
    const index = sessions.findIndex((s) => s.id === sessionId);
    if (index !== -1) {
      sessions[index].title = newTitle;
      state.setState({ chatSessions: sessions }); // Обновляем массив в состоянии
    }
    console.log(
      `ChatHandler: Session ${sessionId} title updated successfully.`
    );
    // Возвращаем Promise без ошибок, чтобы UI не откатывал изменения
    return Promise.resolve();
  } catch (error) {
    console.error(
      `ChatHandler: Error updating session ${sessionId} title:`,
      error
    );
    // Перебрасываем ошибку, чтобы UI мог ее обработать (откатить название)
    throw error;
  }
}

async function handleEditMessage(messageId, newContent, messageElement, event) { // Добавляем event
  console.log(`ChatHandler: Editing message ${messageId}`);

  // Удаляем все последующие сообщения, чтобы ответ был новым
  ui.removeMessagesAfterElement(messageElement);
  ui.disableSendButton(true);
  ui.showTypingIndicator(true);
  state.setState({ activeEditingMessageId: null }); // Выходим из режима редактирования в UI

  console.log(`ChatHandler: Calling api.updateMessage with sessionId: ${state.getState().currentSessionId}, messageId: ${messageId}, newContent: ${newContent}`); // LOGGING
  try {
      if (!messageId || String(messageId).startsWith('temp_')) { // Добавим проверку на temp_ ID
          throw new Error(`Invalid messageId provided for update: ${messageId}`);
      }
      const apiResponse = await api.updateMessage(
          state.getState().currentSessionId,
          messageId,
          newContent
      );

    // Обновляем сообщение в UI и состоянии
    restoreOriginalMessageView(messageElement, newContent); // Восстанавливаем вид с новым текстом
    state.updateMessageInHistory(messageId, newContent);

    // Получаем ID всех последующих сообщений до их удаления
    const currentHistory = state.getState().messageHistory;
    const index = currentHistory.findIndex(m => String(m.id) === String(messageId));
    const idsToDelete = index !== -1
        ? currentHistory.slice(index + 1).filter(m => m.sender === 'assistant').map(m => m.id)
        : [];

    // Удаляем последующие сообщения из UI и состояния
    removeMessagesAfterElement(messageElement);
    state.removeMessagesAfter(messageId);

    // Удаляем их из базы данных
    if (idsToDelete.length > 0) {
        try {
            await api.deleteMessages(state.getState().currentSessionId, idsToDelete);
            console.log('Deleted old assistant replies after edit:', idsToDelete);
        } catch (e) {
            console.warn('Failed to delete old assistant replies after edit:', e);
        }
    }

    // --- Запускаем стриминг для получения нового ответа ---
    const messagesForApi = []; // Историю больше не передаем
    console.log("ChatHandler (Edit): Sending message history to stream API:", messagesForApi);

    let fullAssistantReply = '';

    // Создаем новый элемент для ответа ассистента (как при обычном запросе)
    const assistantTempId = `temp_assistant_${Date.now()}`;
    const assistantMessageElement = addChatMessageElement('assistant', '', assistantTempId);

    api.streamMessage(state.getState().currentSessionId, messagesForApi, {
        onTextChunk: (chunk) => {
            fullAssistantReply += chunk;
            // Отображаем сырой текст во время стриминга при редактировании
            appendChatMessageContent(assistantMessageElement, chunk, false); // Передаем chunk и isFinalChunk=false
            ui.scrollToBottom();
        },
        onAudioChunk: (audioData) => { console.log("Received audio chunk (edit handler)"); },
        onEnd: () => {
            console.log("ChatHandler (Edit): Stream ended.");
            if (fullAssistantReply) {
                // Финальный вызов для форматирования всего текста после редактирования
                console.log("ChatHandler (Edit): Calling final format in onEnd"); // Логгирование
                appendChatMessageContent(assistantMessageElement, '', true); // Пустой chunk, isFinalChunk=true

                // Сохраняем стриминговый ответ ассистента в базу
                // Сохраняем стриминговый ответ ассистента в базу
                api.saveAssistantMessage(state.getState().currentSessionId, fullAssistantReply, assistantTempId)
                    .then(saveResponse => {
                        if (saveResponse && saveResponse.assistant_message_id) {
                            const finalAssistantId = saveResponse.assistant_message_id;
                            console.log(`ChatHandler (Edit): Assistant message saved with ID: ${finalAssistantId}`);
                            updateChatMessageElementId(assistantTempId, finalAssistantId);
                            state.updateMessageHistoryId(assistantTempId, finalAssistantId);
                        } else {
                            console.warn("ChatHandler (Edit): Save response did not contain assistant_message_id.", saveResponse);
                        }
                    })
                    .catch(saveError => {
                        console.error("ChatHandler (Edit): Error saving assistant message:", saveError);
                    });
            } else {
                if (assistantReplyContainer) {
                    assistantReplyContainer.innerHTML = '(Нет ответа от ассистента)';
                }
            }
            ui.showTypingIndicator(false);
            ui.disableSendButton(false);
            ui.focusChatInput();
        },
        onError: (error) => {
            console.error("ChatHandler (Edit): Stream error:", error);
            if (assistantReplyContainer) {
                assistantReplyContainer.innerHTML = `Ошибка потока: ${error.message}`;
            }
            ui.showTypingIndicator(false);
            ui.disableSendButton(false);
            ui.focusChatInput();
        }
    });
    // --- Конец стриминга ---


    // Показываем уведомление возле курсора
    if (event) {
        showNotificationAbove("Отредактировано!", { x: event.clientX, y: event.clientY });
    } else {
        // Фоллбэк, если event не передан (маловероятно)
        showNotificationAbove("Отредактировано!", messageElement.querySelector(".message-actions button"));
    }
  } catch (error) { // Ошибка до начала стриминга (например, при вызове updateMessage)
    console.error(`ChatHandler: Error editing message ${messageId} (before stream):`, error);
    addChatMessageElement(
      "assistant",
      `Не удалось отредактировать сообщение: ${error.message}`,
      `error_${Date.now()}`
    );
    // Восстанавливаем оригинальный вид с оригинальным текстом при ошибке
    const originalContent = messageElement.dataset.originalContent || ""; // Пытаемся получить оригинал
    restoreOriginalMessageView(messageElement, originalContent);
    // Разблокируем UI, так как стрим не запустился
    ui.disableSendButton(false);
    ui.showTypingIndicator(false);
    ui.focusChatInput();
  }
  // finally блок не нужен, так как разблокировка происходит в колбэках стрима или в catch выше
}

function handleCopyMessage(textToCopy, buttonElement, event) { // Добавляем event
  // Проверяем доступность Clipboard API
  if (navigator.clipboard && navigator.clipboard.writeText) {
    // Используем современный Clipboard API
    navigator.clipboard
      .writeText(textToCopy)
      .then(() => {
        // Показываем уведомление возле курсора
        if (event) {
            showNotificationAbove(textManager.getText('notification_copied'), { x: event.clientX, y: event.clientY });
        } else {
             // Фоллбэк
             showNotificationAbove(textManager.getText('notification_copied'), buttonElement);
        }
      })
      .catch((err) => {
        console.error("ChatHandler: Clipboard API failed:", err);
        // Fallback к старому методу
        fallbackCopyText(textToCopy, buttonElement, event);
      });
  } else {
    // Fallback для старых браузеров или HTTP
    console.log("ChatHandler: Clipboard API not available, using fallback");
    fallbackCopyText(textToCopy, buttonElement, event);
  }
}

// Fallback функция для копирования текста
function fallbackCopyText(textToCopy, buttonElement, event) {
  try {
    // Создаем временный textarea
    const textarea = document.createElement('textarea');
    textarea.value = textToCopy;
    textarea.style.position = 'fixed';
    textarea.style.left = '-9999px';
    textarea.style.top = '-9999px';
    document.body.appendChild(textarea);

    // Выделяем и копируем
    textarea.select();
    textarea.setSelectionRange(0, 99999); // Для мобильных устройств

    const successful = document.execCommand('copy');
    document.body.removeChild(textarea);

    if (successful) {
      // Показываем уведомление об успехе
      if (event) {
          showNotificationAbove(textManager.getText('notification_copied'), { x: event.clientX, y: event.clientY });
      } else {
           showNotificationAbove(textManager.getText('notification_copied'), buttonElement);
      }
    } else {
      throw new Error('execCommand failed');
    }
  } catch (err) {
    console.error("ChatHandler: Fallback copy failed:", err);
    // Показываем текст в prompt как последний fallback
    prompt(textManager.getText('copy_manual_instruction') || 'Скопируйте текст вручную:', textToCopy);
  }
}

/**
 * Загружает сессии, обновляет UI и выделяет указанную сессию.
 * Используется для синхронизации списка чатов из других модулей (например, voiceCallHandler).
 * @param {number|string} sessionId ID сессии для выделения.
 */
export async function updateAndHighlightSession(sessionId) {
    console.log(`ChatHandler: External request to update sessions and highlight ${sessionId}`);
    try {
        const sessionsData = await api.getUserSessions();
        const sessions = sessionsData.sessions || [];
        state.setState({ chatSessions: sessions }); // Обновляем состояние
        ui.renderSessionList(sessions, sessionId); // Перерисовываем список с выделением
        ui.highlightActiveSession(sessionId); // Дополнительно убеждаемся, что выделение применено
        console.log(`ChatHandler: Sessions updated and ${sessionId} highlighted.`);
    } catch (error) {
        console.error("ChatHandler: Error updating sessions externally:", error);
        // Не прерываем работу, просто логируем
    }
}

export { loadChatHistory, handleSendMessage };
