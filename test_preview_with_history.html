<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Тест превью с историей сообщений</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f0f0f0;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-case {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        .test-case h3 {
            margin-top: 0;
            color: #333;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .status.success { background: #d4edda; border-left: 4px solid #28a745; }
        .status.error { background: #f8d7da; border-left: 4px solid #dc3545; }
        .status.warning { background: #fff3cd; border-left: 4px solid #ffc107; }
        .status.info { background: #d1ecf1; border-left: 4px solid #17a2b8; }
        
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .danger { background: #dc3545; }
        .danger:hover { background: #c82333; }
        .success { background: #28a745; }
        .success:hover { background: #218838; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Тест превью с историей сообщений</h1>
        <p>Этот тест проверяет новую логику: превью НЕ показываются, если история уже загружена и содержит сообщения.</p>
        
        <div class="test-case">
            <h3>Тест-кейс 1: Новый пользователь (пустая история)</h3>
            <p><strong>Ожидаемый результат:</strong> Превью сообщения ДОЛЖНЫ показаться</p>
            <button onclick="testCase1()" class="success">🆕 Тест нового пользователя</button>
            <div id="test1-result"></div>
        </div>
        
        <div class="test-case">
            <h3>Тест-кейс 2: Пользователь с историей сообщений</h3>
            <p><strong>Ожидаемый результат:</strong> Превью сообщения НЕ ДОЛЖНЫ показаться</p>
            <button onclick="testCase2()" class="danger">📚 Тест с историей</button>
            <div id="test2-result"></div>
        </div>
        
        <div class="test-case">
            <h3>Тест-кейс 3: Ручной тест превью</h3>
            <p><strong>Описание:</strong> Принудительный вызов функции превью</p>
            <button onclick="testCase3()">🎯 Ручной тест</button>
            <div id="test3-result"></div>
        </div>
        
        <div class="container">
            <h3>🔧 Утилиты</h3>
            <button onclick="clearStorage()" class="danger">🗑️ Очистить localStorage</button>
            <button onclick="simulateHistory()" class="warning">📝 Симулировать историю</button>
            <button onclick="showCurrentState()">📊 Показать текущее состояние</button>
            <button onclick="openChat()">💬 Открыть чат</button>
        </div>
        
        <div class="container">
            <h3>📋 Лог тестов</h3>
            <button onclick="clearLog()">🗑️ Очистить лог</button>
            <div id="test-log"></div>
        </div>
    </div>

    <script>
        let testLog = [];
        
        function logTest(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            testLog.push({ timestamp, message, type });
            updateTestLog();
            console.log(`[TEST ${timestamp}] ${message}`);
        }
        
        function updateTestLog() {
            const logDiv = document.getElementById('test-log');
            logDiv.innerHTML = testLog.map(test => 
                `<div class="status ${test.type}">
                    <strong>${test.timestamp}:</strong> ${test.message}
                </div>`
            ).join('');
        }
        
        function clearLog() {
            testLog = [];
            updateTestLog();
        }
        
        function updateTestResult(testId, message, type = 'info') {
            const resultDiv = document.getElementById(`${testId}-result`);
            resultDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
        }
        
        function clearStorage() {
            localStorage.clear();
            sessionStorage.clear();
            logTest('localStorage и sessionStorage очищены', 'success');
        }
        
        function simulateHistory() {
            // Симулируем наличие истории сообщений
            const fakeHistory = [
                { id: 1, message: 'Привет!', role: 'user', timestamp: Date.now() - 60000 },
                { id: 2, message: 'Здравствуйте! Как дела?', role: 'assistant', timestamp: Date.now() - 30000 }
            ];
            
            const chatState = {
                currentSessionId: 'test-session-123',
                messageHistory: fakeHistory,
                isChatOpen: true
            };
            
            localStorage.setItem('chatState', JSON.stringify(chatState));
            logTest('Симулирована история с 2 сообщениями', 'warning');
        }
        
        async function showCurrentState() {
            try {
                // Импортируем функции состояния
                const { getState } = await import('./chat-admin/chat-js/state.js');
                const currentState = getState();
                
                const stateInfo = `
                    Текущее состояние:
                    • currentSessionId: ${currentState.currentSessionId || 'нет'}
                    • messageHistory: ${currentState.messageHistory ? currentState.messageHistory.length : 0} сообщений
                    • isChatOpen: ${currentState.isChatOpen}
                    • chatSessions: ${currentState.chatSessions ? currentState.chatSessions.length : 0}
                `;
                
                logTest(stateInfo, 'info');
                
            } catch (error) {
                logTest('Ошибка получения состояния: ' + error.message, 'error');
            }
        }
        
        async function openChat() {
            try {
                const { openChatWindow } = await import('./chat-admin/chat-js/uiChat.js');
                openChatWindow();
                logTest('Чат открыт вручную', 'success');
            } catch (error) {
                logTest('Ошибка открытия чата: ' + error.message, 'error');
            }
        }
        
        async function testCase1() {
            logTest('=== ТЕСТ-КЕЙС 1: Новый пользователь ===', 'info');
            
            // Очищаем все
            clearStorage();
            
            try {
                // Открываем чат
                const { openChatWindow } = await import('./chat-admin/chat-js/uiChat.js');
                openChatWindow();
                
                logTest('Чат открыт для нового пользователя', 'success');
                updateTestResult('test1', '✅ Тест запущен. Превью должны появиться через 3-6 секунд.', 'success');
                
                // Проверяем через 10 секунд
                setTimeout(() => {
                    const previewMessages = document.querySelectorAll('.czn-chat-message-preview');
                    if (previewMessages.length > 0) {
                        logTest(`✅ ТЕСТ-КЕЙС 1 ПРОЙДЕН: Найдено ${previewMessages.length} превью сообщений`, 'success');
                        updateTestResult('test1', `✅ ТЕСТ ПРОЙДЕН: Найдено ${previewMessages.length} превью сообщений`, 'success');
                    } else {
                        logTest('❌ ТЕСТ-КЕЙС 1 НЕ ПРОЙДЕН: Превью сообщения не найдены', 'error');
                        updateTestResult('test1', '❌ ТЕСТ НЕ ПРОЙДЕН: Превью сообщения не найдены', 'error');
                    }
                }, 10000);
                
            } catch (error) {
                logTest('❌ Ошибка в тест-кейсе 1: ' + error.message, 'error');
                updateTestResult('test1', '❌ Ошибка: ' + error.message, 'error');
            }
        }
        
        async function testCase2() {
            logTest('=== ТЕСТ-КЕЙС 2: Пользователь с историей ===', 'info');
            
            // Симулируем историю
            simulateHistory();
            
            try {
                // Открываем чат
                const { openChatWindow } = await import('./chat-admin/chat-js/uiChat.js');
                openChatWindow();
                
                logTest('Чат открыт для пользователя с историей', 'warning');
                updateTestResult('test2', '⏳ Тест запущен. Превью НЕ должны появиться.', 'warning');
                
                // Проверяем через 10 секунд
                setTimeout(() => {
                    const previewMessages = document.querySelectorAll('.czn-chat-message-preview');
                    if (previewMessages.length === 0) {
                        logTest('✅ ТЕСТ-КЕЙС 2 ПРОЙДЕН: Превью сообщения не показались (как и ожидалось)', 'success');
                        updateTestResult('test2', '✅ ТЕСТ ПРОЙДЕН: Превью сообщения не показались (правильно!)', 'success');
                    } else {
                        logTest(`❌ ТЕСТ-КЕЙС 2 НЕ ПРОЙДЕН: Найдено ${previewMessages.length} превью сообщений (не должно быть)`, 'error');
                        updateTestResult('test2', `❌ ТЕСТ НЕ ПРОЙДЕН: Найдено ${previewMessages.length} превью сообщений`, 'error');
                    }
                }, 10000);
                
            } catch (error) {
                logTest('❌ Ошибка в тест-кейсе 2: ' + error.message, 'error');
                updateTestResult('test2', '❌ Ошибка: ' + error.message, 'error');
            }
        }
        
        async function testCase3() {
            logTest('=== ТЕСТ-КЕЙС 3: Ручной тест ===', 'info');
            
            try {
                const { checkAndShowPreviewForEmptyChat } = await import('./chat-admin/chat-js/uiChat.js');
                await checkAndShowPreviewForEmptyChat();
                
                logTest('Ручной тест превью выполнен', 'success');
                updateTestResult('test3', '✅ Ручной тест выполнен. Проверьте консоль для деталей.', 'success');
                
            } catch (error) {
                logTest('❌ Ошибка ручного теста: ' + error.message, 'error');
                updateTestResult('test3', '❌ Ошибка: ' + error.message, 'error');
            }
        }
    </script>

    <!-- Подключаем чат -->
    <script type="module" src="chat-admin/chat-js/chat-loader.js"></script>
</body>
</html>
