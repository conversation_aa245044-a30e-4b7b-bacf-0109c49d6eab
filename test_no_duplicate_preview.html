<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Тест отсутствия дублирования превью</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f0f0f0;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid;
        }
        .success { background: #d4edda; border-color: #28a745; }
        .error { background: #f8d7da; border-color: #dc3545; }
        .warning { background: #fff3cd; border-color: #ffc107; }
        .info { background: #d1ecf1; border-color: #17a2b8; }
        
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .danger { background: #dc3545; }
        .danger:hover { background: #c82333; }
        
        #log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Тест отсутствия дублирования превью</h1>
        <p>Проверяем, что превью НЕ показываются, если в чате уже есть сообщения.</p>
        
        <div class="status info">
            <strong>Цель теста:</strong> Убедиться, что после исправления дублирующих вызовов в chatHandler.js превью показываются только когда чат действительно пустой.
        </div>
        
        <button onclick="clearAll()" class="danger">🗑️ Очистить всё</button>
        <button onclick="simulateEmptyChat()">📭 Симулировать пустой чат</button>
        <button onclick="simulateChatWithHistory()">📚 Симулировать чат с историей</button>
        <button onclick="openChat()">💬 Открыть чат</button>
        <button onclick="checkPreviewCount()">🔍 Проверить количество превью</button>
        <button onclick="clearLog()">🗑️ Очистить лог</button>
        
        <div class="container">
            <h3>📊 Результаты тестов</h3>
            <div id="results"></div>
        </div>
        
        <div class="container">
            <h3>📋 Лог</h3>
            <div id="log"></div>
        </div>
    </div>

    <script>
        let logEntries = [];
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            logEntries.push(`[${timestamp}] ${message}`);
            updateLog();
            console.log(`[TEST] ${message}`);
        }
        
        function updateLog() {
            document.getElementById('log').innerHTML = logEntries.join('<br>');
        }
        
        function clearLog() {
            logEntries = [];
            updateLog();
        }
        
        function updateResults(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
        }
        
        function clearAll() {
            localStorage.clear();
            sessionStorage.clear();
            
            // Удаляем все превью из DOM
            const previewMessages = document.querySelectorAll('.czn-chat-message-preview');
            previewMessages.forEach(msg => msg.remove());
            
            // Удаляем все сообщения из DOM
            const allMessages = document.querySelectorAll('.czn-chat-message');
            allMessages.forEach(msg => msg.remove());
            
            log('✅ Всё очищено: localStorage, sessionStorage, DOM', 'success');
            updateResults('✅ Всё очищено', 'success');
        }
        
        function simulateEmptyChat() {
            clearAll();
            
            const emptyState = {
                currentSessionId: null,
                messageHistory: [],
                chatSessions: [],
                isChatOpen: false
            };
            
            localStorage.setItem('chatState', JSON.stringify(emptyState));
            log('📭 Симулирован пустой чат (нет сессий, нет истории)', 'info');
            updateResults('📭 Пустой чат симулирован. Теперь откройте чат - превью ДОЛЖНЫ появиться.', 'info');
        }
        
        function simulateChatWithHistory() {
            clearAll();
            
            const stateWithHistory = {
                currentSessionId: 'test-session-123',
                messageHistory: [
                    { id: 1, message: 'Привет!', role: 'user', timestamp: Date.now() - 60000 },
                    { id: 2, message: 'Здравствуйте!', role: 'assistant', timestamp: Date.now() - 30000 }
                ],
                chatSessions: [
                    { id: 'test-session-123', title: 'Тестовая сессия', created_at: Date.now() - 120000 }
                ],
                isChatOpen: false
            };
            
            localStorage.setItem('chatState', JSON.stringify(stateWithHistory));
            log('📚 Симулирован чат с историей (2 сообщения)', 'warning');
            updateResults('📚 Чат с историей симулирован. Теперь откройте чат - превью НЕ ДОЛЖНЫ появиться.', 'warning');
        }
        
        async function openChat() {
            try {
                const { openChatWindow } = await import('./chat-admin/chat-js/uiChat.js');
                openChatWindow();
                log('💬 Чат открыт', 'success');
                
                // Проверяем через 10 секунд
                setTimeout(() => {
                    checkPreviewCount();
                }, 10000);
                
            } catch (error) {
                log('❌ Ошибка открытия чата: ' + error.message, 'error');
                updateResults('❌ Ошибка открытия чата: ' + error.message, 'error');
            }
        }
        
        function checkPreviewCount() {
            const previewMessages = document.querySelectorAll('.czn-chat-message-preview');
            const allMessages = document.querySelectorAll('.czn-chat-message');
            const nonPreviewMessages = document.querySelectorAll('.czn-chat-message:not(.czn-chat-message-preview)');
            
            const currentState = JSON.parse(localStorage.getItem('chatState') || '{}');
            const historyLength = currentState.messageHistory ? currentState.messageHistory.length : 0;
            
            log(`🔍 Проверка: превью=${previewMessages.length}, всего сообщений=${allMessages.length}, не-превью=${nonPreviewMessages.length}, история=${historyLength}`);
            
            let result = '';
            let resultType = 'info';
            
            if (historyLength === 0 && nonPreviewMessages.length === 0) {
                // Пустой чат - превью должны быть
                if (previewMessages.length >= 2) {
                    result = `✅ ТЕСТ ПРОЙДЕН: Пустой чат, превью показаны (${previewMessages.length})`;
                    resultType = 'success';
                } else {
                    result = `❌ ТЕСТ НЕ ПРОЙДЕН: Пустой чат, но превью не показаны (${previewMessages.length})`;
                    resultType = 'error';
                }
            } else {
                // Чат с сообщениями - превью не должны быть
                if (previewMessages.length === 0) {
                    result = `✅ ТЕСТ ПРОЙДЕН: Чат с сообщениями, превью не показаны (правильно!)`;
                    resultType = 'success';
                } else {
                    result = `❌ ТЕСТ НЕ ПРОЙДЕН: Чат с сообщениями, но превью показаны (${previewMessages.length}) - ОШИБКА!`;
                    resultType = 'error';
                }
            }
            
            log(result, resultType);
            updateResults(result, resultType);
        }
        
        // Автоматический мониторинг изменений в DOM
        let observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach(function(node) {
                        if (node.nodeType === 1 && node.classList && node.classList.contains('czn-chat-message-preview')) {
                            log(`👀 Обнаружено добавление превью сообщения: ${node.textContent.substring(0, 50)}...`);
                        }
                    });
                }
            });
        });
        
        // Начинаем наблюдение за изменениями в DOM
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
        
        log('🚀 Тест инициализирован. Начните с очистки всего, затем симулируйте сценарий.');
    </script>

    <!-- Подключаем чат -->
    <script type="module" src="chat-admin/chat-js/chat-loader.js"></script>
</body>
</html>
