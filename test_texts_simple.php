<?php
declare(strict_types=1);

require_once 'chat-admin/app/Models/Database.php';
require_once 'chat-admin/app/Models/TextModel.php';

use App\Models\Database;
use App\Models\TextModel;

try {
    $db = Database::getInstance();
    $textModel = new TextModel();
    
    echo "Проверка превью текстов:\n";
    echo "========================\n";
    
    $preview1 = $textModel->getTextByKey('preview_message_1');
    $preview2 = $textModel->getTextByKey('preview_message_2');
    
    echo "preview_message_1: " . ($preview1 ? $preview1['text'] : 'НЕ НАЙДЕН') . "\n";
    echo "preview_message_2: " . ($preview2 ? $preview2['text'] : 'НЕ НАЙДЕН') . "\n";
    
} catch (Exception $e) {
    echo "ОШИБКА: " . $e->getMessage() . "\n";
}
?>
